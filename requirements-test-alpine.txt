# Alpine Linux compatible test requirements
# Fallback file for when main requirements-test.txt fails on Alpine

# Build tools - essential for Alpine
setuptools<60.0
wheel>=0.37.0
Cython<3.0.0

# Core libraries with known Alpine compatibility
numpy>=1.24.0,<1.27.0
pandas>=2.0.0,<2.2.0

# PySpark - should work on Alpine
pyspark==3.2.4

# Testing framework - minimal set
pytest>=7.0.0,<8.0.0
pytest-cov>=4.0.0,<5.0.0
pytest-mock>=3.10.0,<4.0.0

# Essential utilities
python-dateutil>=2.8.0
pytz>=2023.0
mock>=5.0.0

# Minimal logging
json-logging>=1.3.0

# Redis for mocking
redis>=5.0.0,<6.0.0
