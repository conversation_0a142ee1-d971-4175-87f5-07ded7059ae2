# CI/CD Test Environment Setup

This document explains the setup for running tests in both local development and CI/CD environments, specifically addressing numpy build issues in Jenkins.

## Problem

The original issue was that n<PERSON><PERSON> was trying to build from source in the Jenkins environment, causing failures due to missing BLAS libraries:

```
Building wheel for numpy (pyproject.toml): finished with status 'error'
libraries openblas not found
libraries mkl_rt not found
```

## Solution

We've implemented a unified approach that works for both local development and CI environments:

### 1. Single Requirements File

- `requirements-test.txt`: Works for both local and CI environments
- Uses specific versions known to have pre-compiled wheels
- Includes setuptools version constraint (`<60.0`) to avoid numpy build issues

### 2. Installation Script

- `install_test_deps.sh`: Single comprehensive script with multiple fallback strategies
- Tries 4 different installation approaches for each package:
  1. Binary-only with specific version
  2. Prefer-binary with specific version
  3. Latest version with binary-only
  4. Any available version
- Includes verification steps and detailed logging

### 3. Jenkins Pipeline Updates

The Jenkins pipeline now:
1. Creates a virtual environment
2. Uses the dedicated installation script with built-in binary preferences
3. Runs tests with proper environment variables

## Key Changes

### Jenkinsfile.development

- Added pip configuration setup
- Uses `install_test_deps.sh` for dependency installation
- Exports `PIP_CONFIG_FILE` environment variable

### Requirements File

- `requirements-test.txt`: Single file that works for both local and CI environments
- Includes setuptools constraint and build tools for compatibility

### Installation Strategy

1. **Upgrade core tools**: pip, setuptools, wheel
2. **Install setuptools<60.0**: Prevents numpy build issues
3. **Install packages with 4-tier fallback**:
   - Try binary-only with specific version
   - Try prefer-binary with specific version
   - Try latest version with binary-only
   - Try any available version (last resort)
4. **Critical packages**: pytest and pyspark (must succeed)
5. **Optional packages**: Other utilities (warnings if they fail)

## Usage

### Both Local Development and CI/CD

```bash
# Use the installation script (recommended)
./install_test_deps.sh
```

### Alternative: Direct pip installation

```bash
# For local development
pip install -r requirements-test.txt
```

### Manual Installation (if script fails)

```bash
source .venv/bin/activate
pip install --upgrade pip setuptools wheel
pip install --prefer-binary "setuptools<60.0"
pip install --prefer-binary --timeout=300 numpy==1.26.4
pip install --prefer-binary --timeout=300 pandas==2.1.4
pip install --prefer-binary --timeout=300 -r requirements-test.txt
```

## Troubleshooting

### If numpy still tries to build from source:

1. Check pip version: `pip --version` (should be latest)
2. Clear pip cache: `pip cache purge`
3. Use explicit flags: `pip install --only-binary=:all: --no-build-isolation numpy==1.24.4`
4. Check available wheels: `pip index versions numpy`

### If tests fail to run:

1. Verify Python path: `echo $PYTHONPATH`
2. Check Spark configuration: `echo $SPARK_LOCAL_IP`
3. Verify package installations: `pip list | grep -E "(numpy|pandas|pytest|pyspark)"`

## Environment Variables

The following environment variables are set in the Jenkins pipeline:

- `PIP_CONFIG_FILE`: Points to our custom pip configuration
- `SPARK_LOCAL_IP`: Set to 127.0.0.1 for local Spark testing
- `PYTHONPATH`: Points to the src directory
- `ENABLE_JSON_LOGGING`: Disabled for testing

## Files Overview

- `requirements-test.txt`: Unified test dependencies for local and CI
- `install_test_deps.sh`: Robust installation script using pip command-line flags
- `Jenkinsfile.development`: Updated Jenkins pipeline
- `CI_SETUP.md`: This documentation file
