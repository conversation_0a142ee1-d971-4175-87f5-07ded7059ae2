apiVersion: v1
kind: Pod
metadata:
  name: jenkins-slave
  labels:
    app: jenkins-slave
spec:
  nodeSelector:
    node.pluang.org/instancegroup: components-node
  containers:
    - name: "aws"
      image: "public.ecr.aws/c8p0n4f3/aws-jnlp-slave:17-jdk-alpine-aws-kctl-helm-v4"
      imagePullPolicy: "IfNotPresent"
      command:
        - "sleep"
      args:
        - "9999999"
      volumeMounts:
        - mountPath: "/home/<USER>/agent"
          name: "workspace-volume"
          readOnly: false
        - mountPath: "/home/<USER>/.aws/"
          name: aws-secrets
          readOnly: true
    - name: kaniko
      image: gcr.io/kaniko-project/executor:v1.22.0-debug
      imagePullPolicy: IfNotPresent
      command:
        - sleep
      args:
        - 9999999
      volumeMounts:
        - mountPath: "/root/.aws/"
          name: aws-secrets
          readOnly: true
          # - name: jenkins-docker-cfg
          #   mountPath: /kaniko/.docker
      resources:
        requests:
          memory: 1000Mi
          cpu: 500m
    - name: node
      image: public.ecr.aws/c8p0n4f3/node:10-alpine
      imagePullPolicy: IfNotPresent
      command:
        - sleep
      args:
        - 9999999
  volumes:
    - name: jenkins-docker-cfg
      projected:
        sources:
          - secret:
              name: ecr-pull-secret
              items:
                - key: .dockercfg
                  path: config.json
    - name: aws-secrets
      projected:
        sources:
          - secret:
              name: aws-secret
          - secret:
              name: aws-config
