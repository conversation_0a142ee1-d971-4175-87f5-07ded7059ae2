#!/bin/bash

# Script to install test dependencies with robust error handling
# Works for both local development and CI environments

set -e  # Exit on any error

echo "🔧 Installing test dependencies with binary-preferred policy..."

# Activate virtual environment
source .venv/bin/activate

# Upgrade core tools first
echo "📦 Upgrading pip, setuptools, and wheel..."
pip install --upgrade pip setuptools wheel

# Install setuptools with version constraint to avoid numpy build issues
echo "🔧 Installing compatible setuptools..."
pip install --prefer-binary "setuptools<60.0"

# Install numpy and pandas with timeout and binary preference
# These are the most problematic packages that often fail to build
# Use fallback to binary-only if prefer-binary fails
echo "🔢 Installing numpy with binary preference..."
pip install --prefer-binary --timeout=300 numpy==1.24.4 || \
pip install --only-binary=:all: --timeout=300 numpy==1.24.4

echo "🐼 Installing pandas with binary preference..."
pip install --prefer-binary --timeout=300 pandas==2.1.3 || \
pip install --only-binary=:all: --timeout=300 pandas==2.1.3

# Install remaining test dependencies with binary preference and timeout
echo "🧪 Installing remaining test dependencies..."
pip install --prefer-binary --timeout=300 -r requirements-test.txt

echo "✅ All test dependencies installed successfully!"

# Verify critical packages
echo "🔍 Verifying installations..."
python -c "import numpy; print(f'NumPy version: {numpy.__version__}')"
python -c "import pandas; print(f'Pandas version: {pandas.__version__}')"
python -c "import pytest; print(f'Pytest version: {pytest.__version__}')"
python -c "import pyspark; print(f'PySpark version: {pyspark.__version__}')"

echo "🎉 Test environment setup complete!"

# Optionally run setup verification test
if [ "$1" = "--verify" ]; then
    echo ""
    echo "🔍 Running setup verification test..."
    python test_setup.py
fi
