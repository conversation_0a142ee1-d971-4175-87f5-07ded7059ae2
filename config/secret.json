{"env": "local", "bucket": "***", "offset": 0, "plus_member_tag_id": [10018], "plus_member_tag_name": ["PLUANG PLUS ACTIVE USERS"], "bootstrap_servers": "*******", "gold_maintenance_fees_kafka_topic": "gold-dormant-fee-staging", "num_s3_partitions": 3, "num_mongo_partitions": 15, "kafka_keystore_type": "****", "kafka_key_password": "****", "kafka_keystore_password": "****", "kafka_keystore_location": "****", "kafka_truststore_password": "****", "kafka_truststore_location": "****", "kafka_security_protocol": "****", "trading_competition": {"offset": 1, "frequency": 24, "id": "2025-002", "start_time": "2025-06-22 17:00:00.000000", "end_time": "2025-09-30 16:59:59.999999"}, "kafka_topics": {"crypto_future_instruments_topic": "***", "global_stock_topic": "***", "mission_events_topic": "***", "forex_yield_ingestion_topic": "***", "daily_statement_topic": "***", "monthly_statement_topic": "***", "crypto_currencies_topic": "***", "funds_topic": "***", "crypto_futures_price_topic": "***", "clevertap_events_topic": "***", "customer_risk_rating_topic": "***"}, "data_store": {"pricing_mongo": {"host": "***", "username": "***", "password": "***"}, "crypto_futures_pricing_mongo": {"host": "*****", "username": "******", "password": "*******"}, "reporting_mongo": {"host": "***", "username": "***", "password": "***"}, "gold_postgres": {"host": "********", "username": "***", "password": "***"}, "fund_postgres": {"host": "********", "username": "***", "password": "***"}, "forex_postgres": {"host": "********", "username": "***", "password": "***"}, "redis": {"host": "***", "port": "***", "user": "***", "password": "***"}, "current_indo_stock_price": {"collection": "***.***"}, "crypto_currency": {"collection": "***.***"}, "crypto_currency_futures": {"collection": "***.***"}, "gold": {"collection": "***.***"}, "forex": {"db_table": "***.***"}, "fund": {"db_table": "***.***"}, "global_stock": {"collection": "***.***"}, "trading_competition": {"collection": "***.***"}, "flash_game": {"collection": "***.***"}}, "flash_games": {"FG001": {"assets": {"global_stocks": [10055, 10719], "global_stock_options": []}, "start_ts": "2025-07-14 08:00:00.000000", "end_ts": "2025-07-19 00:00:00.000000", "schedule_end_ts": "2025-07-19 02:00:00.000000"}}}