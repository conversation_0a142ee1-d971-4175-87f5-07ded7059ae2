# Jenkins Alpine Linux Test Fix

## Problem
The Jenkins `run-tests` stage was failing because n<PERSON><PERSON> was trying to build from source on Alpine Linux, which lacks the necessary BLAS/LAPACK libraries and development tools.

## Root Cause
- Jenkins agent uses Alpine Linux container (`aws-jnlp-slave:17-jdk-alpine-aws-kctl-helm-v4`)
- Alpine uses `musl` libc instead of `glibc`, causing wheel compatibility issues
- numpy 1.24.4 had limited `musllinux` wheel support
- Missing system development libraries for building from source

## Solution Applied

### 1. Updated Dependencies (`requirements-test.txt`)
- **numpy**: `1.24.4` → `1.26.4` (better musllinux wheel support)
- **pandas**: `2.1.3` → `2.1.4` (compatible with newer numpy)

### 2. Enhanced Installation Script (`install_test_deps.sh`)
- **Alpine Detection**: Automatically detects Alpine Linux environment
- **System Dependencies**: Installs required build tools and libraries:
  - `gcc`, `g++`, `musl-dev`, `linux-headers`
  - `gfortran`, `openblas-dev`, `lapack-dev`
  - Graphics libraries: `freetype-dev`, `libpng-dev`, `jpeg-dev`, `zlib-dev`
- **Multi-Strategy Installation**:
  1. Try `--prefer-binary` first
  2. Alpine-specific strategy with separate numpy/pandas installation
  3. Binary-only fallback
  4. Alpine-specific requirements fallback
  5. Default installation as last resort
- **Better Error Handling**: Comprehensive verification and debugging info

### 3. Updated Jenkins Pipeline (`Jenkinsfile.development`)
- **System Dependencies**: Pre-installs Alpine packages before Python setup
- **Robust Error Handling**: Continues even if some packages fail

### 4. Alpine Fallback Requirements (`requirements-test-alpine.txt`)
- Simplified, Alpine-compatible package versions
- Used as fallback if main requirements fail

## Expected Behavior

### Success Path
1. Jenkins detects Alpine Linux
2. Installs system dependencies (gcc, openblas, etc.)
3. Creates Python virtual environment
4. Installs numpy 1.26.4 and pandas 2.1.4 from wheels
5. Installs remaining test dependencies
6. Runs tests successfully

### Debug Information
The script now provides detailed environment information:
- OS and architecture
- Python and pip versions
- Alpine version (if applicable)
- Package verification results

## Testing the Fix

Run the Jenkins pipeline and look for these success indicators:
```
🏔️ Detected Alpine Linux - installing system dependencies...
✅ System dependencies installed
📦 Installing test dependencies from requirements-test.txt...
✅ All dependencies installed successfully with prefer-binary
✅ NumPy version: 1.26.4
✅ Pandas version: 2.1.4
✅ Pytest version: 7.4.3
✅ PySpark version: 3.2.4
🎉 Test environment setup complete!
```

## Fallback Strategies

If the main fix doesn't work, the script will automatically try:
1. Alpine-specific numpy/pandas installation
2. Binary-only installation
3. Simplified Alpine requirements
4. Default pip installation

## Maintenance Notes

- **numpy/pandas versions**: Keep updated to versions with good musllinux support
- **System dependencies**: May need updates if new packages are added
- **Alpine compatibility**: Monitor Alpine Linux updates that might affect builds
